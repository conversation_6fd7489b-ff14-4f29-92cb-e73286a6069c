import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { fetchFeed, setFeedType } from '../store/slices/feedSlice';
import { Button } from '../components/ui/Button';
import { PlusIcon } from '@heroicons/react/24/outline';
import CreatePost from '../components/posts/CreatePost';
import Post from '../components/posts/Post';

const FeedPage = () => {
  const dispatch = useDispatch();
  const { posts, isLoading, hasMore } = useSelector((state) => state.feed);
  const { user } = useSelector((state) => state.auth);

  const [page, setPage] = useState(1);
  const [selectedTab, setSelectedTab] = useState('all');
  const [viewingCommentsForPostId, setViewingCommentsForPostId] = useState(null);
  const [showCreatePost, setShowCreatePost] = useState(false);

  useEffect(() => {
    dispatch(fetchFeed({ page: 1, type: selectedTab }));
    setPage(1);
  }, [dispatch, selectedTab]);

  const handleTabChange = (tab) => {
    setSelectedTab(tab);
    dispatch(setFeedType(tab));
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop < document.documentElement.offsetHeight - 500 || isLoading) {
        return;
      }
      if (hasMore) {
        setPage(prevPage => prevPage + 1);
      } else {
        // Recycle posts
        setPage(1);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isLoading, hasMore]);

  useEffect(() => {
    if (page > 1) {
      dispatch(fetchFeed({ page, type: selectedTab }));
    }
  }, [dispatch, page, selectedTab]);



  const handleViewComments = (postId) => {
    setViewingCommentsForPostId(postId);
  };

  const feedTabs = [
    { id: 'all', label: 'For You', description: 'Personalized content' },
    { id: 'following', label: 'Following', description: 'From people you follow' },
    { id: 'trending', label: 'Trending', description: 'Popular right now' },
    { id: 'communities', label: 'Communities', description: 'From your communities' },
  ];

  return (
    <div className="max-w-3xl mx-auto p-4">
      {/* Welcome message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-white mb-2">
          Welcome back, {user?.realName}!
        </h1>
        <p className="text-gray-400">
          Discover what's happening in your network
        </p>
      </motion.div>

      {/* Feed tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-800">
          <nav className="-mb-px flex space-x-8">
            {feedTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Create post section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-background rounded-lg shadow-sm border border-gray-800 p-6 mb-6"
      >
        <div className="flex items-center space-x-4">
          <img
            src={user?.avatar || '/default-avatar.png'}
            alt={user?.username}
            className="w-12 h-12 rounded-full"
          />
          <button
            onClick={() => setShowCreatePost(true)}
            className="flex-1 text-left p-3 border border-gray-700 rounded-lg bg-gray-800 text-gray-400 hover:bg-gray-700 transition-colors"
          >
            What's on your mind?
          </button>
          <Button
            onClick={() => setShowCreatePost(true)}
            className="flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create</span>
          </Button>
        </div>
      </motion.div>

      {/* Feed content */}
      <div className="space-y-6">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="bg-background rounded-lg shadow-sm border border-gray-800 p-6"
            >
              <div className="animate-pulse">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-gray-800 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-800 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-800 rounded w-1/6"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-800 rounded"></div>
                  <div className="h-4 bg-gray-800 rounded w-3/4"></div>
                </div>
                <div className="h-48 bg-gray-800 rounded-lg mt-4"></div>
              </div>
            </div>
          ))
        ) : posts && posts.length > 0 ? (
          posts.map((post) => (
            <Post
              key={post._id}
              post={post}
              onViewComments={handleViewComments}
              showComments={viewingCommentsForPostId === post._id}
            />
          ))
        ) : (
          // Empty state
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">📱</div>
            <h3 className="text-xl font-semibold text-white mb-2">
              No posts yet
            </h3>
            <p className="text-gray-400 mb-6">
              Start following people or join communities to see posts in your feed
            </p>
            <Button onClick={() => window.location.href = '/explore'}>
              Discover People
            </Button>
          </motion.div>
        )}
      </div>

      {/* Pagination loader */}
      {isLoading && page > 1 && (
        <div className="text-center py-4">
          <p className="text-gray-400">Loading more posts...</p>
        </div>
      )}

      {/* Create Post Modal */}
      <AnimatePresence>
        {showCreatePost && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={(e) => e.target === e.currentTarget && setShowCreatePost(false)}
          >
            <CreatePost onClose={() => setShowCreatePost(false)} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FeedPage;

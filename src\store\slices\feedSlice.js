import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Async thunks
export const fetchFeed = createAsyncThunk(
  'feed/fetchFeed',
  async ({ page = 1, type = 'all' }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/feed?page=${page}&type=${type}`);
      return { posts: response.data.posts, hasMore: response.data.hasMore, page };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch feed');
    }
  }
);

export const createPost = createAsyncThunk(
  'feed/createPost',
  async (postData, { rejectWithValue, getState }) => {
    try {
      // For now, create a mock post since backend isn't ready
      const { auth } = getState();
      const user = auth.user;

      const newPost = {
        _id: Date.now().toString(),
        content: postData.content || postData.get?.('content') || '',
        type: postData.type || postData.get?.('type') || 'text',
        author: {
          _id: user._id,
          username: user.username,
          realName: user.realName,
          avatar: user.avatar,
          isFollowed: false
        },
        createdAt: new Date().toISOString(),
        likesCount: 0,
        commentsCount: 0,
        sharesCount: 0,
        isLiked: false,
        privacy: postData.privacy || postData.get?.('privacy') || 'public',
        location: postData.location || postData.get?.('location') || '',
        tags: postData.tags || (postData.get?.('tags') ? JSON.parse(postData.get('tags')) : []),
      };

      // Handle different post types
      if (newPost.type === 'poll' && (postData.poll || postData.get?.('poll'))) {
        const pollData = postData.poll || JSON.parse(postData.get('poll'));
        newPost.poll = {
          ...pollData,
          votes: pollData.options.map(() => ({ count: 0, voters: [] })),
          totalVotes: 0,
          hasVoted: false
        };
      }

      if (newPost.type === 'event' && (postData.event || postData.get?.('event'))) {
        const eventData = postData.event || JSON.parse(postData.get('event'));
        newPost.event = {
          ...eventData,
          attendees: [],
          isAttending: false
        };
      }

      // Handle media files (mock URLs for now)
      if (postData instanceof FormData) {
        const mediaFiles = [];
        for (let [key, value] of postData.entries()) {
          if (key.startsWith('media_') && value instanceof File) {
            mediaFiles.push({
              type: value.type.startsWith('image/') ? 'image' : 'video',
              url: URL.createObjectURL(value), // Mock URL - in production this would be uploaded to server
              name: value.name
            });
          }
        }
        if (mediaFiles.length > 0) {
          newPost.media = mediaFiles;
        }
      }

      // Handle scheduled posts
      if (postData.scheduledFor || postData.get?.('scheduledFor')) {
        newPost.scheduledFor = postData.scheduledFor || postData.get('scheduledFor');
        newPost.status = 'scheduled';
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return newPost;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create post');
    }
  }
);

export const likePost = createAsyncThunk(
  'feed/likePost',
  async (postId, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/posts/${postId}/like`);
      return { postId, liked: response.data.liked, likesCount: response.data.likesCount };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to like post');
    }
  }
);

export const sharePost = createAsyncThunk(
  'feed/sharePost',
  async (postId, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/posts/${postId}/share`);
      return { postId, sharesCount: response.data.sharesCount };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to share post');
    }
  }
);

export const fetchComments = createAsyncThunk(
  'feed/fetchComments',
  async ({ postId, page = 1 }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/posts/${postId}/comments?page=${page}`);
      return { postId, comments: response.data.comments, hasMore: response.data.hasMore };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch comments');
    }
  }
);

export const addComment = createAsyncThunk(
  'feed/addComment',
  async ({ postId, content }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/posts/${postId}/comments`, { content });
      return { postId, comment: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add comment');
    }
  }
);

export const fetchCommunityPosts = createAsyncThunk(
  'feed/fetchCommunityPosts',
  async ({ communityId, page = 1 }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/communities/${communityId}/posts?page=${page}`);
      return { posts: response.data.posts, hasMore: response.data.hasMore, page };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch community posts');
    }
  }
);

const initialState = {
  posts: [],
  isLoading: false,
  isLoadingMore: false,
  hasMore: true,
  currentPage: 1,
  feedType: 'all',
  error: null,
  comments: {},
  trending: [],
};

const feedSlice = createSlice({
  name: 'feed',
  initialState,
  reducers: {
    setFeedType: (state, action) => {
      state.feedType = action.payload;
      state.posts = [];
      state.currentPage = 1;
      state.hasMore = true;
    },
    updatePost: (state, action) => {
      const { postId, updates } = action.payload;
      const postIndex = state.posts.findIndex(p => p._id === postId);
      if (postIndex !== -1) {
        state.posts[postIndex] = { ...state.posts[postIndex], ...updates };
      }
    },
    removePost: (state, action) => {
      const postId = action.payload;
      state.posts = state.posts.filter(p => p._id !== postId);
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch feed
      .addCase(fetchFeed.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          state.isLoading = true;
        } else {
          state.isLoadingMore = true;
        }
        state.error = null;
      })
      .addCase(fetchFeed.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        const { posts, hasMore, page } = action.payload;
        
        if (page === 1) {
          state.posts = posts;
        } else {
          state.posts = [...state.posts, ...posts];
        }
        
        state.hasMore = hasMore;
        state.currentPage = page;
      })
      .addCase(fetchFeed.rejected, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        state.error = action.payload;
      })
      // Create post
      .addCase(createPost.fulfilled, (state, action) => {
        state.posts.unshift(action.payload);
      })
      // Like post
      .addCase(likePost.fulfilled, (state, action) => {
        const { postId, liked, likesCount } = action.payload;
        const post = state.posts.find(p => p._id === postId);
        if (post) {
          post.liked = liked;
          post.likesCount = likesCount;
        }
      })
      // Share post
      .addCase(sharePost.fulfilled, (state, action) => {
        const { postId, sharesCount } = action.payload;
        const post = state.posts.find(p => p._id === postId);
        if (post) {
          post.sharesCount = sharesCount;
        }
      })
      // Fetch comments
      .addCase(fetchComments.fulfilled, (state, action) => {
        const { postId, comments } = action.payload;
        state.comments[postId] = comments;
      })
      // Add comment
      .addCase(addComment.fulfilled, (state, action) => {
        const { postId, comment } = action.payload;
        if (!state.comments[postId]) {
          state.comments[postId] = [];
        }
        state.comments[postId].unshift(comment);
        
        // Update comments count in post
        const post = state.posts.find(p => p._id === postId);
        if (post) {
          post.commentsCount = (post.commentsCount || 0) + 1;
        }
      })
      // Fetch community posts
      .addCase(fetchCommunityPosts.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          state.isLoading = true;
        } else {
          state.isLoadingMore = true;
        }
        state.error = null;
      })
      .addCase(fetchCommunityPosts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        const { posts, hasMore, page } = action.payload;
        if (page === 1) {
          state.posts = posts;
        } else {
          state.posts = [...state.posts, ...posts];
        }
        state.hasMore = hasMore;
        state.currentPage = page;
      })
      .addCase(fetchCommunityPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        state.error = action.payload;
      });
  },
});

export const {
  setFeedType,
  updatePost,
  removePost,
  clearError,
} = feedSlice.actions;

export default feedSlice.reducer;
